import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

// GET /api/products - Get all products (public endpoint)
export async function GET() {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('is_active', true)
      .order('category', { ascending: true })
      .order('name', { ascending: true });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Error al obtener los productos' },
        { status: 500 }
      );
    }

    // Generate signed URLs for images if they exist
    const productsWithImages = await Promise.all(
      (data || []).map(async product => {
        if (product.image_path) {
          try {
            // If image_path is already a full URL, use it as is
            if (product.image_path.startsWith('http')) {
              return product;
            }

            // Otherwise, generate a signed URL
            const { data: signedUrl } = await supabase.storage
              .from('products')
              .createSignedUrl(product.image_path, 60 * 60 * 24 * 365); // 1 year

            return {
              ...product,
              image_path: signedUrl?.signedUrl || product.image_path,
            };
          } catch (imageError) {
            console.error(
              'Error generating signed URL for product:',
              product.id,
              imageError
            );
            return product;
          }
        }
        return product;
      })
    );

    return NextResponse.json({
      success: true,
      data: productsWithImages,
      count: productsWithImages?.length || 0,
    });
  } catch (error) {
    console.error('Products API error:', error);
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido',
      },
      { status: 500 }
    );
  }
}
