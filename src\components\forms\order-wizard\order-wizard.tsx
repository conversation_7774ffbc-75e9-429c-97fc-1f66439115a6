'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { OrderFormData } from '@/types/order-form';
import { OrderFormDataSchema } from '@/lib/validation/order-schemas';
import { useOrderWizardStore } from '@/stores/order-wizard-store';
import { OrderWizardStep1 } from './step1-customer-info';
import { OrderWizardStep2 } from './step2-delivery-address';
import { OrderWizardStep3 } from './step3-products';
import { OrderWizardStep4 } from './step4-delivery-options';
import { OrderWizardStep5 } from './step5-multi-stop';
import { OrderWizardStep6 } from './step6-payment';
import { OrderWizardStep7 } from './step7-additional-notes';
import { OrderWizardStep8 } from './step8-mexican-logistics';
import { OrderWizardStep9 } from './step9-review';
import { z } from 'zod';

interface OrderWizardProps {
  onSubmit: (data: OrderFormData) => void;
  onCancel: () => void;
  loading?: boolean;
  initialData?: OrderFormData;
}

export function OrderWizard({
  onSubmit,
  onCancel,
  loading = false,
  initialData,
}: OrderWizardProps) {
  // Use Zustand store for state management
  const {
    formData,
    currentStep,
    errors,
    isSubmitting,
    updateFormData,
    setCurrentStep,
    setErrors,
    setIsSubmitting,
    clearErrors,
    resetWizard,
  } = useOrderWizardStore();

  // Initialize with any provided initial data
  React.useEffect(() => {
    if (initialData && Object.keys(initialData).length > 0) {
      updateFormData(initialData);
    }
  }, [initialData, updateFormData]);

  const totalSteps = 9;

  const nextStep = async () => {
    if (currentStep < totalSteps) {
      // Validate current step before proceeding
      const isValid = await validateStep(currentStep);
      if (isValid) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    // Validate all steps before submitting
    setIsSubmitting(true);
    const isValid = await validateAllSteps();
    if (isValid) {
      onSubmit(formData);
      // Clear saved data after successful submission
      resetWizard();
    }
    setIsSubmitting(false);
  };

  // Validation functions
  const validateStep = async (step: number): Promise<boolean> => {
    setIsSubmitting(true);
    clearErrors();

    try {
      switch (step) {
        case 1:
          // Validate customer info
          OrderFormDataSchema.pick({
            customer_name: true,
            customer_phone: true,
            customer_email: true,
          }).parse(formData);
          break;
        case 2:
          // Validate addresses
          OrderFormDataSchema.pick({
            pickup_address: true,
            delivery_address: true,
          }).parse(formData);
          break;
        case 3:
          // Validate products
          OrderFormDataSchema.pick({
            products: true,
          }).parse(formData);
          break;
        case 4:
          // Validate delivery options
          OrderFormDataSchema.pick({
            delivery_date: true,
            delivery_time_slot: true,
            delivery_mode: true,
          }).parse(formData);
          break;
        case 5:
          // Step 5: Delivery Management - No validation required
          // Users can select delivery mode without additional validation
          break;
        case 6:
          // Validate payment method and invoice requirements
          const paymentValidation = OrderFormDataSchema.pick({
            payment_method: true,
            invoice_required: true,
          });

          // Only validate fiscal data if invoice is required
          if (formData.invoice_required) {
            const paymentWithFiscalValidation = OrderFormDataSchema.pick({
              payment_method: true,
              invoice_required: true,
              fiscal_data: true,
            });
            paymentWithFiscalValidation.parse(formData);
          } else {
            paymentValidation.parse(formData);
          }
          break;
        case 7:
          // Validate additional notes
          OrderFormDataSchema.pick({
            special_instructions: true,
            allow_substitutions: true,
          }).parse(formData);
          break;
        case 8:
          // Validate Mexican logistics
          OrderFormDataSchema.pick({
            vehicle_type_id: true,
            cargo_type_id: true,
            route_optimization: true,
            delivery_region: true,
          }).parse(formData);
          break;
        case 9:
          // Validate terms acceptance
          OrderFormDataSchema.pick({
            terms_accepted: true,
          }).parse(formData);

          // Ensure terms are accepted
          if (!formData.terms_accepted) {
            throw new z.ZodError([
              {
                code: 'custom',
                message:
                  'Debes aceptar los términos y condiciones para continuar',
                path: ['terms_accepted'],
              },
            ]);
          }
          break;
      }
      setIsSubmitting(false);
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors: Record<string, string> = {};
        error.issues.forEach(issue => {
          if (issue.path) {
            fieldErrors[issue.path.join('.')] = issue.message;
          }
        });
        setErrors(fieldErrors);
      }
      setIsSubmitting(false);
      return false;
    }
  };

  const validateAllSteps = async (): Promise<boolean> => {
    setIsSubmitting(true);
    clearErrors();

    try {
      // Debug: Log the form data before validation
      console.log(
        '🔍 Validating form data:',
        JSON.stringify(formData, null, 2)
      );

      // Validate entire form
      OrderFormDataSchema.parse(formData);
      console.log('✅ Validation passed successfully');
      setIsSubmitting(false);
      return true;
    } catch (error) {
      console.error('❌ Validation failed:', error);
      if (error instanceof z.ZodError) {
        const fieldErrors: Record<string, string> = {};
        console.log('🐛 Validation errors:', error.issues);
        error.issues.forEach(issue => {
          if (issue.path) {
            fieldErrors[issue.path.join('.')] = issue.message;
            console.log(
              `❌ Field error: ${issue.path.join('.')} - ${issue.message}`
            );
          }
        });
        setErrors(fieldErrors);
      }
      setIsSubmitting(false);
      return false;
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <OrderWizardStep1
            formData={formData}
            updateFormData={updateFormData}
            errors={errors}
          />
        );
      case 2:
        return (
          <OrderWizardStep2
            formData={formData}
            updateFormData={updateFormData}
            errors={errors}
          />
        );
      case 3:
        return (
          <OrderWizardStep3
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 4:
        return (
          <OrderWizardStep4
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 5:
        return (
          <OrderWizardStep5
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 6:
        return (
          <OrderWizardStep6
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 7:
        return (
          <OrderWizardStep7
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 8:
        return (
          <OrderWizardStep8
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 9:
        return (
          <OrderWizardStep9
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      default:
        return (
          <OrderWizardStep1
            formData={formData}
            updateFormData={updateFormData}
            errors={errors}
          />
        );
    }
  };

  return (
    <div className='max-w-4xl mx-auto p-6 space-y-6'>
      {/* Progress Bar */}
      <div className='mb-8'>
        <div className='flex justify-between items-center mb-2'>
          <h1 className='text-2xl font-bold text-gray-900'>
            Crear Nuevo Pedido - Paso {currentStep} de {totalSteps}
          </h1>
          <span className='text-sm text-gray-500'>
            {Math.round((currentStep / totalSteps) * 100)}% completado
          </span>
        </div>
        <div className='w-full bg-gray-200 rounded-full h-2.5'>
          <div
            className='bg-blue-600 h-2.5 rounded-full transition-all duration-300'
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          ></div>
        </div>
        {Object.keys(errors).length > 0 && (
          <div className='mt-2 text-sm text-red-600'>
            Por favor corrige los errores antes de continuar
          </div>
        )}
      </div>

      {/* Step Content */}
      <div className='space-y-6'>{renderStep()}</div>

      {/* Navigation Buttons */}
      <div className='flex justify-between pt-6'>
        <Button
          type='button'
          variant='outline'
          onClick={currentStep === 1 ? onCancel : prevStep}
          disabled={loading || isSubmitting}
        >
          {currentStep === 1 ? 'Cancelar' : 'Anterior'}
        </Button>

        {currentStep < totalSteps ? (
          <Button
            type='button'
            onClick={nextStep}
            disabled={loading || isSubmitting}
          >
            {isSubmitting ? (
              <div className='flex items-center gap-2'>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
                Validando...
              </div>
            ) : (
              'Siguiente'
            )}
          </Button>
        ) : (
          <Button
            type='button'
            onClick={handleSubmit}
            disabled={loading || isSubmitting}
          >
            {loading || isSubmitting ? (
              <div className='flex items-center gap-2'>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
                {loading ? 'Creando...' : 'Validando...'}
              </div>
            ) : (
              'Crear Pedido'
            )}
          </Button>
        )}
      </div>
    </div>
  );
}
