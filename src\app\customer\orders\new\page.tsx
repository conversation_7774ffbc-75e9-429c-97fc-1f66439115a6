'use client';

import { useState, useCallback, useMemo, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { OrderWizard } from '@/components/forms/order-wizard';
import { createClient } from '@/utils/supabase/client';
import { OrderFormData } from '@/types/order-form';
import {
  apiClient,
  transformToMultiStopApiFormat,
  isMultiStopOrder,
} from '@/lib/api-client';

export default function NewOrderPage() {
  const { user, profile, loading } = useAuthStore();
  const router = useRouter();
  const [creating, setCreating] = useState(false);

  // Redirect to home page if user is not authenticated
  useEffect(() => {
    if (!loading && (!user || !profile)) {
      router.push('/');
    }
  }, [user, profile, loading, router]);

  // Memoize the Supabase client to prevent recreation on every render
  const supabase = useMemo(() => createClient(), []);

  const handleOrderSubmit = useCallback(
    async (orderData: OrderFormData) => {
      if (!user) return;

      setCreating(true);
      try {
        // Prepare order data with customer ID
        const orderWithCustomerId = {
          ...orderData,
          customer_id: user.id,
        };

        let result;

        // Check if this is a multi-stop order
        if (isMultiStopOrder(orderData)) {
          // Transform data for multi-stop API format
          const multiStopData =
            transformToMultiStopApiFormat(orderWithCustomerId);
          result = await apiClient.createMultiStopOrder(multiStopData);
        } else {
          // Use regular order API
          result = await apiClient.createOrder(orderWithCustomerId);
        }

        if (!result.success) {
          throw new Error(result.error || 'Error al crear el pedido');
        }

        // Redirect to order tracking page
        const orderId = result.data?.id;
        if (orderId) {
          router.push(`/customer/orders/${orderId}/tracking`);
        } else {
          router.push('/customer/orders');
        }
      } catch (error) {
        console.error('Error creating order:', error);
        alert(
          error instanceof Error
            ? error.message
            : 'Error al crear el pedido. Por favor intenta de nuevo.'
        );
      } finally {
        setCreating(false);
      }
    },
    [user, router]
  );

  const handleCancel = useCallback(() => {
    router.push('/customer/dashboard');
  }, [router]);

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-black'></div>
      </div>
    );
  }

  // Show nothing while redirecting
  if (!user || !profile) {
    return null;
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='max-w-6xl mx-auto'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-gray-900 mb-2'>
            🚚 Crear Pedido de Logística
          </h1>
          <p className='text-gray-600'>
            Sistema completo de logística mexicana con seguimiento en tiempo
            real
          </p>
        </div>

        <OrderWizard
          onSubmit={handleOrderSubmit}
          onCancel={handleCancel}
          loading={creating}
        />
      </div>
    </div>
  );
}
