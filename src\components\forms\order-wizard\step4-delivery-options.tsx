'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { OrderFormData } from '@/types/order-form';

interface Step4Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
}

export function OrderWizardStep4({ formData, updateFormData }: Step4Props) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          ⏰ Opciones de Entrega
        </CardTitle>
        <CardDescription>
          Selecciona la fecha, hora y modalidad de entrega
        </CardDescription>
      </CardHeader>
      <CardContent className='grid grid-cols-1 md:grid-cols-2 gap-4'>
        <div className='space-y-2'>
          <Label htmlFor='delivery_date'>Fecha de Entrega</Label>
          <Input
            id='delivery_date'
            type='date'
            value={formData.delivery_date}
            onChange={e =>
              updateFormData({
                delivery_date: e.target.value,
              })
            }
            required
          />
        </div>
        <div className='space-y-2'>
          <Label htmlFor='delivery_time_slot'>Franja Horaria</Label>
          <select
            id='delivery_time_slot'
            value={formData.delivery_time_slot || ''}
            onChange={e =>
              updateFormData({
                delivery_time_slot: e.target.value as any,
              })
            }
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            required
          >
            <option value=''>Seleccionar horario</option>
            <option value='08:00-10:00'>08:00 - 10:00</option>
            <option value='10:00-12:00'>10:00 - 12:00</option>
            <option value='12:00-14:00'>12:00 - 14:00</option>
            <option value='14:00-16:00'>14:00 - 16:00</option>
            <option value='16:00-18:00'>16:00 - 18:00</option>
            <option value='18:00-20:00'>18:00 - 20:00</option>
          </select>
        </div>
        <div className='space-y-2 md:col-span-2'>
          <Label>Modalidad de Entrega</Label>
          <div className='flex gap-4'>
            <label className='flex items-center space-x-2'>
              <input
                type='radio'
                name='delivery_mode'
                value='domicilio'
                checked={formData.delivery_mode === 'domicilio'}
                onChange={e =>
                  updateFormData({
                    delivery_mode: e.target.value as any,
                  })
                }
                className='text-blue-600'
              />
              <span>🏠 Entrega a domicilio</span>
            </label>
            <label className='flex items-center space-x-2'>
              <input
                type='radio'
                name='delivery_mode'
                value='punto_de_conveniencia'
                checked={formData.delivery_mode === 'punto_de_conveniencia'}
                onChange={e =>
                  updateFormData({
                    delivery_mode: e.target.value as any,
                  })
                }
                className='text-blue-600'
              />
              <span>🏪 Recoger en punto de conveniencia</span>
            </label>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
