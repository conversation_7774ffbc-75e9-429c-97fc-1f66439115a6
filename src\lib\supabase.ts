import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
});

// Types for our database
export type Database = {
  public: {
    Tables: {
      customer_profiles: {
        Row: {
          created_at: string | null;
          customer_tier: string | null;
          date_of_birth: string | null;
          emergency_contact_name: string | null;
          emergency_contact_phone: string | null;
          id: string;
          language_preference: string | null;
          loyalty_points: number | null;
          marketing_opt_in: boolean | null;
          notification_preferences: {
            sms?: boolean;
            push?: boolean;
            email?: boolean;
          } | null;
          preferred_delivery_time: string | null;
          preferred_name: string | null;
          referral_code: string | null;
          referred_by_code: string | null;
          total_orders: number | null;
          total_spent: number | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          customer_tier?: string | null;
          date_of_birth?: string | null;
          emergency_contact_name?: string | null;
          emergency_contact_phone?: string | null;
          id?: string;
          language_preference?: string | null;
          loyalty_points?: number | null;
          marketing_opt_in?: boolean | null;
          notification_preferences?: {
            sms?: boolean;
            push?: boolean;
            email?: boolean;
          } | null;
          preferred_delivery_time?: string | null;
          preferred_name?: string | null;
          referral_code?: string | null;
          referred_by_code?: string | null;
          total_orders?: number | null;
          total_spent?: number | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          customer_tier?: string | null;
          date_of_birth?: string | null;
          emergency_contact_name?: string | null;
          emergency_contact_phone?: string | null;
          id?: string;
          language_preference?: string | null;
          loyalty_points?: number | null;
          marketing_opt_in?: boolean | null;
          notification_preferences?: {
            sms?: boolean;
            push?: boolean;
            email?: boolean;
          } | null;
          preferred_delivery_time?: string | null;
          preferred_name?: string | null;
          referral_code?: string | null;
          referred_by_code?: string | null;
          total_orders?: number | null;
          total_spent?: number | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'customer_profiles_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: true;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      orders: {
        Row: {
          created_at: string | null;
          customer_id: string;
          delivery_addresses: {
            address: string;
            city: string;
            state: string;
            zip_code: string;
            country: string;
          }[];
          delivery_id: string | null;
          id: string;
          package_details: {
            weight: number;
            dimensions: string;
            description: string;
            fragile: boolean;
          } | null;
          payment_method: string | null;
          payment_status: string | null;
          pickup_address: {
            address: string;
            city: string;
            state: string;
            zip_code: string;
            country: string;
          };
          status: string | null;
          total_cost: number | null;
          updated_at: string | null;
          // Mexican Logistics Fields
          vehicle_type_id: string | null;
          cargo_type_id: string | null;
          delivery_mode: string | null;
          scheduled_pickup_time: string | null;
          scheduled_delivery_time: string | null;
          delivery_instructions: string | null;
          special_handling_notes: string | null;
          fiscal_data: Record<string, unknown> | null;
          payment_method_details: Record<string, unknown> | null;
          stops: Record<string, unknown>[] | null;
          route_optimization: Record<string, unknown> | null;
          tracking_number: string | null;
          delivery_zone: string | null;
          delivery_region: string | null;
        };
        Insert: {
          created_at?: string | null;
          customer_id: string;
          delivery_addresses: {
            address: string;
            city: string;
            state: string;
            zip_code: string;
            country: string;
          }[];
          delivery_id?: string | null;
          id?: string;
          package_details?: {
            weight: number;
            dimensions: string;
            description: string;
            fragile: boolean;
          } | null;
          payment_method?: string | null;
          payment_status?: string | null;
          pickup_address: {
            address: string;
            city: string;
            state: string;
            zip_code: string;
            country: string;
          };
          status?: string | null;
          total_cost?: number | null;
          updated_at?: string | null;
          // Mexican Logistics Fields
          vehicle_type_id?: string | null;
          cargo_type_id?: string | null;
          delivery_mode?: string | null;
          scheduled_pickup_time?: string | null;
          scheduled_delivery_time?: string | null;
          delivery_instructions?: string | null;
          special_handling_notes?: string | null;
          fiscal_data?: Record<string, unknown> | null;
          payment_method_details?: Record<string, unknown> | null;
          stops?: Record<string, unknown>[] | null;
          route_optimization?: Record<string, unknown> | null;
          tracking_number?: string | null;
          delivery_zone?: string | null;
          delivery_region?: string | null;
        };
        Update: {
          created_at?: string | null;
          customer_id?: string;
          delivery_addresses?: {
            address: string;
            city: string;
            state: string;
            zip_code: string;
            country: string;
          }[];
          delivery_id?: string | null;
          id?: string;
          package_details?: {
            weight: number;
            dimensions: string;
            description: string;
            fragile: boolean;
          } | null;
          payment_method?: string | null;
          payment_status?: string | null;
          pickup_address?: {
            address: string;
            city: string;
            state: string;
            zip_code: string;
            country: string;
          };
          status?: string | null;
          total_cost?: number | null;
          updated_at?: string | null;
          // Mexican Logistics Fields
          vehicle_type_id?: string | null;
          cargo_type_id?: string | null;
          delivery_mode?: string | null;
          scheduled_pickup_time?: string | null;
          scheduled_delivery_time?: string | null;
          delivery_instructions?: string | null;
          special_handling_notes?: string | null;
          fiscal_data?: Record<string, unknown> | null;
          payment_method_details?: Record<string, unknown> | null;
          stops?: Record<string, unknown>[] | null;
          route_optimization?: Record<string, unknown> | null;
          tracking_number?: string | null;
          delivery_zone?: string | null;
          delivery_region?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'orders_customer_id_fkey';
            columns: ['customer_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'orders_delivery_id_fkey';
            columns: ['delivery_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'orders_vehicle_type_id_fkey';
            columns: ['vehicle_type_id'];
            isOneToOne: false;
            referencedRelation: 'vehicle_types';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'orders_cargo_type_id_fkey';
            columns: ['cargo_type_id'];
            isOneToOne: false;
            referencedRelation: 'cargo_types';
            referencedColumns: ['id'];
          },
        ];
      };
      profiles: {
        Row: {
          created_at: string | null;
          email: string;
          full_name: string | null;
          id: string;
          is_active: boolean | null;
          phone: string | null;
          role: string | null;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          email: string;
          full_name?: string | null;
          id: string;
          is_active?: boolean | null;
          phone?: string | null;
          role?: string | null;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          email?: string;
          full_name?: string | null;
          id?: string;
          is_active?: boolean | null;
          phone?: string | null;
          role?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      user_wallets: {
        Row: {
          balance: number;
          created_at: string | null;
          id: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          balance?: number;
          created_at?: string | null;
          id?: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          balance?: number;
          created_at?: string | null;
          id?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'user_wallets_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: true;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      wallet_transactions: {
        Row: {
          amount: number;
          created_at: string | null;
          description: string | null;
          id: string;
          metadata: Record<string, unknown> | null;
          order_id: string | null;
          status: string | null;
          stripe_payment_intent_id: string | null;
          transaction_ref: string | null;
          type: string;
          updated_at: string | null;
          user_id: string;
          wallet_id: string;
        };
        Insert: {
          amount: number;
          created_at?: string | null;
          description?: string | null;
          id?: string;
          metadata?: Record<string, unknown> | null;
          order_id?: string | null;
          status?: string | null;
          stripe_payment_intent_id?: string | null;
          transaction_ref?: string | null;
          type: string;
          updated_at?: string | null;
          user_id: string;
          wallet_id: string;
        };
        Update: {
          amount?: number;
          created_at?: string | null;
          description?: string | null;
          id?: string;
          metadata?: Record<string, unknown> | null;
          order_id?: string | null;
          status?: string | null;
          stripe_payment_intent_id?: string | null;
          transaction_ref?: string | null;
          type?: string;
          updated_at?: string | null;
          user_id?: string;
          wallet_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'wallet_transactions_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'wallet_transactions_wallet_id_fkey';
            columns: ['wallet_id'];
            isOneToOne: false;
            referencedRelation: 'user_wallets';
            referencedColumns: ['id'];
          },
        ];
      };
      webhook_logs: {
        Row: {
          id: string;
          event_type: string;
          stripe_event_id: string;
          stripe_object_id: string | null;
          status: string;
          metadata: Record<string, unknown> | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          event_type: string;
          stripe_event_id: string;
          stripe_object_id?: string | null;
          status?: string;
          metadata?: Record<string, unknown> | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          event_type?: string;
          stripe_event_id?: string;
          stripe_object_id?: string | null;
          status?: string;
          metadata?: Record<string, unknown> | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      password_reset_audit_logs: {
        Row: {
          id: string;
          event_type: string;
          user_id: string | null;
          email: string | null;
          ip_address: string | null;
          user_agent: string | null;
          metadata: Record<string, unknown> | null;
          jwt_context: Record<string, unknown> | null;
          success: boolean;
          error_message: string | null;
          created_at: string | null;
        };
        Insert: {
          id?: string;
          event_type: string;
          user_id?: string | null;
          email?: string | null;
          ip_address?: string | null;
          user_agent?: string | null;
          metadata?: Record<string, unknown> | null;
          jwt_context?: Record<string, unknown> | null;
          success: boolean;
          error_message?: string | null;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          event_type?: string;
          user_id?: string | null;
          email?: string | null;
          ip_address?: string | null;
          user_agent?: string | null;
          metadata?: Record<string, unknown> | null;
          jwt_context?: Record<string, unknown> | null;
          success?: boolean;
          error_message?: string | null;
          created_at?: string | null;
        };
        Relationships: [];
      };
      vehicle_types: {
        Row: {
          id: string;
          name: string;
          category: string;
          max_weight_kg: number;
          max_volume_m3: number;
          base_rate_per_km: number;
          special_capabilities: string[] | null;
          description: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          name: string;
          category: string;
          max_weight_kg: number;
          max_volume_m3: number;
          base_rate_per_km?: number;
          special_capabilities?: string[] | null;
          description?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          name?: string;
          category?: string;
          max_weight_kg?: number;
          max_volume_m3?: number;
          base_rate_per_km?: number;
          special_capabilities?: string[] | null;
          description?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      cargo_types: {
        Row: {
          id: string;
          name: string;
          category: string;
          special_requirements: Record<string, unknown> | null;
          rate_multiplier: number;
          description: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          name: string;
          category: string;
          special_requirements?: Record<string, unknown> | null;
          rate_multiplier?: number;
          description?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          name?: string;
          category?: string;
          special_requirements?: Record<string, unknown> | null;
          rate_multiplier?: number;
          description?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      products: {
        Row: {
          id: string;
          name: string;
          category: string | null;
          description: string | null;
          unit_price: number;
          unit_measure:
            | 'kg'
            | 'g'
            | 'pieza'
            | 'paquete'
            | 'litro'
            | 'ml'
            | 'unidad'
            | 'caja'
            | 'bulto';
          weight: number;
          weight_unit: 'kg' | 'g';
          dimensions: {
            length: number;
            width: number;
            height: number;
            unit: 'cm' | 'm';
          } | null;
          image_path: string | null;
          is_active: boolean | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          name: string;
          category?: string | null;
          description?: string | null;
          unit_price: number;
          unit_measure:
            | 'kg'
            | 'g'
            | 'pieza'
            | 'paquete'
            | 'litro'
            | 'ml'
            | 'unidad'
            | 'caja'
            | 'bulto';
          weight: number;
          weight_unit: 'kg' | 'g';
          dimensions?: {
            length: number;
            width: number;
            height: number;
            unit: 'cm' | 'm';
          } | null;
          image_path?: string | null;
          is_active?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          name?: string;
          category?: string | null;
          description?: string | null;
          unit_price?: number;
          unit_measure?:
            | 'kg'
            | 'g'
            | 'pieza'
            | 'paquete'
            | 'litro'
            | 'ml'
            | 'unidad'
            | 'caja'
            | 'bulto';
          weight?: number;
          weight_unit?: 'kg' | 'g';
          dimensions?: {
            length: number;
            width: number;
            height: number;
            unit: 'cm' | 'm';
          } | null;
          image_path?: string | null;
          is_active?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      armor: {
        Args: { '': string };
        Returns: string;
      };
      dearmor: {
        Args: { '': string };
        Returns: string;
      };
      gen_random_bytes: {
        Args: { '': number };
        Returns: string;
      };
      gen_random_uuid: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      gen_salt: {
        Args: { '': string };
        Returns: string;
      };
      pgp_armor_headers: {
        Args: { '': string };
        Returns: Record<string, unknown>[];
      };
      pgp_key_id: {
        Args: { '': string };
        Returns: string;
      };
      uuid_generate_v1: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      uuid_generate_v1mc: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      uuid_generate_v3: {
        Args: { name: string; namespace: string };
        Returns: string;
      };
      uuid_generate_v4: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      uuid_generate_v5: {
        Args: { name: string; namespace: string };
        Returns: string;
      };
      uuid_nil: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      uuid_ns_dns: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      uuid_ns_oid: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      uuid_ns_url: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      uuid_ns_x500: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

// Add types for cost calculation
export interface CostCalculationRequest {
  origin: {
    lat: number;
    lng: number;
    address: string;
  };
  destination: {
    lat: number;
    lng: number;
    address: string;
  };
  vehicleTypeId?: string;
  cargoTypeId?: string;
  packageWeight?: number;
  packageVolume?: number;
  deliveryMode?: 'primera-milla' | 'media-milla' | 'ultima-milla';
  scheduledTime?: string;
  stops?: Array<{
    lat: number;
    lng: number;
    address: string;
  }>;
}

export interface CostBreakdown {
  distanceCost: number;
  vehicleCost: number;
  cargoCost: number;
  deliveryModeCost: number;
  timeCost: number;
  stopsCost: number;
  total: number;
}

export interface CostCalculationResponse {
  totalCost: number;
  currency: string;
  breakdown: CostBreakdown;
  distance: number;
  estimatedDeliveryTime: string;
}
