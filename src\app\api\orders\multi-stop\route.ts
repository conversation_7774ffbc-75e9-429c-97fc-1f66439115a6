import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  console.log('Multi-stop API called');

  try {
    const body = await request.json();
    console.log('Request body received');

    return NextResponse.json({
      success: true,
      message: 'Multi-stop API is working',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error in multi-stop API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
