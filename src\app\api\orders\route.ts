import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import {
  secureAPI,
  sanitizeInput,
  isValidUUID,
} from '@/lib/security/api-security';
import { auditLog } from '@/lib/security/audit-logger';

// Define interfaces for the request body
interface Address {
  street?: string;
  number?: string;
  colony?: string;
  city?: string;
  state?: string;
  zip?: string;
  references?: string;
}

interface Product {
  id: string;
  name: string;
  quantity: number;
  price: number;
  category?: string;
}

interface FiscalData {
  rfc?: string;
  business_name?: string;
  tax_regime?: string;
}

interface CreateOrderRequestBody {
  customer_name?: string;
  customer_phone?: string;
  customer_email?: string;
  customer_type?: string;
  pickup_address?: Address;
  delivery_address?: Address;
  products?: Product[];
  delivery_mode?: string;
  delivery_date?: string;
  delivery_time_slot?: string;
  pickup_time_slot?: string;
  payment_method?: string;
  invoice_required?: boolean;
  fiscal_data?: FiscalData;
  special_instructions?: string;
  allow_substitutions?: boolean;
  communication_preferences?: string[];
  vehicle_type_id?: string;
  cargo_type_id?: string;
  route_optimization?: any;
  delivery_region?: string;
  regulatory_requirements?: any;
  total_cost?: number;
  estimated_delivery_time?: string;
  terms_accepted?: boolean;
}

// POST /api/orders - Create a regular order
export const POST = secureAPI(
  {
    POST: async (request, context) => {
      try {
        // Parse and sanitize request body
        let requestBody: CreateOrderRequestBody;
        try {
          requestBody = sanitizeInput(
            await request.json()
          ) as CreateOrderRequestBody;
        } catch {
          return NextResponse.json(
            { error: 'Formato de cuerpo de solicitud inválido' },
            { status: 400 }
          );
        }

        const {
          customer_name,
          customer_phone,
          customer_email,
          customer_type,
          pickup_address,
          delivery_address,
          products,
          delivery_mode,
          delivery_date,
          delivery_time_slot,
          pickup_time_slot,
          payment_method,
          invoice_required,
          fiscal_data,
          special_instructions,
          allow_substitutions,
          communication_preferences,
          vehicle_type_id,
          cargo_type_id,
          route_optimization,
          delivery_region,
          regulatory_requirements,
          total_cost,
          estimated_delivery_time,
          terms_accepted,
        } = requestBody;

        // Validate required fields
        if (
          !customer_name ||
          !customer_phone ||
          !pickup_address ||
          !delivery_address
        ) {
          return NextResponse.json(
            {
              error:
                'Los campos customer_name, customer_phone, pickup_address y delivery_address son requeridos',
            },
            { status: 400 }
          );
        }

        // Validate UUID fields if provided
        if (vehicle_type_id && !isValidUUID(vehicle_type_id)) {
          return NextResponse.json(
            { error: 'ID de tipo de vehículo inválido' },
            { status: 400 }
          );
        }

        if (cargo_type_id && !isValidUUID(cargo_type_id)) {
          return NextResponse.json(
            { error: 'ID de tipo de carga inválido' },
            { status: 400 }
          );
        }

        const supabase = await createClient();

        // Generate tracking number
        const trackingNumber = `MX${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

        // Create the order
        const orderData = {
          customer_id: context.user?.id,
          tracking_number: trackingNumber,
          customer_name,
          customer_phone,
          customer_email: customer_email || context.user?.email,
          customer_type: customer_type || 'individual',
          pickup_address,
          delivery_address,
          products: products || [],
          delivery_mode: delivery_mode || 'standard',
          delivery_date,
          delivery_time_slot,
          pickup_time_slot,
          payment_method: payment_method || 'cash',
          invoice_required: invoice_required || false,
          fiscal_data: fiscal_data || null,
          special_instructions: special_instructions || null,
          allow_substitutions: allow_substitutions || false,
          communication_preferences: communication_preferences || [],
          vehicle_type_id: vehicle_type_id || null,
          cargo_type_id: cargo_type_id || null,
          route_optimization: route_optimization || null,
          delivery_region: delivery_region || null,
          regulatory_requirements: regulatory_requirements || null,
          total_cost: total_cost || 0,
          estimated_delivery_time: estimated_delivery_time || null,
          terms_accepted: terms_accepted || false,
          status: 'pending',
          payment_status: 'pending',
          created_at: new Date().toISOString(),
        };

        const { data, error } = await supabase
          .from('orders')
          .insert([orderData])
          .select()
          .single();

        if (error) {
          console.error('Database error:', error);
          await auditLog({
            event: 'ORDER_CREATE_ERROR',
            userId: context.user?.id,
            ip: context.request.ip,
            details: { error: error.message, requestData: requestBody },
            severity: 'MEDIUM',
            category: 'DATA',
          });
          return NextResponse.json(
            { error: 'Error al crear la orden' },
            { status: 500 }
          );
        }

        // Create initial tracking record
        await supabase.from('order_tracking').insert({
          order_id: data.id,
          status: 'created',
          timestamp: new Date().toISOString(),
          notes: 'Pedido creado exitosamente',
        });

        await auditLog({
          event: 'ORDER_CREATED',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            orderId: data.id,
            customerId: context.user?.id,
            trackingNumber,
          },
          severity: 'MEDIUM',
          category: 'DATA',
        });

        return NextResponse.json(
          {
            success: true,
            message: 'Orden creada exitosamente',
            data,
          },
          { status: 201 }
        );
      } catch (error) {
        await auditLog({
          event: 'ORDER_API_ERROR',
          userId: context.user?.id,
          ip: context.request.ip,
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
          severity: 'HIGH',
          category: 'SYSTEM',
        });

        return NextResponse.json(
          {
            error: 'Error interno del servidor',
            details:
              error instanceof Error ? error.message : 'Error desconocido',
          },
          { status: 500 }
        );
      }
    },
  },
  {
    requireAuth: true,
    allowedRoles: ['admin', 'customer'],
    requireMFA: false,
    validateCSRF: true,
    rateLimitRpm: 30,
  }
);
