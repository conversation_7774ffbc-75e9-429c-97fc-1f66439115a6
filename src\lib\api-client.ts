import { createClient } from '@/utils/supabase/client';
import type { OrderFormData } from '@/types/order-form';

// API Request/Response Types
interface CreateOrderRequest {
  customer_name?: string;
  customer_phone?: string;
  customer_email?: string;
  customer_type?: string;
  pickup_address?: {
    street?: string;
    number?: string;
    colony?: string;
    city?: string;
    state?: string;
    zip?: string;
    references?: string;
  };
  delivery_address?: {
    street?: string;
    number?: string;
    colony?: string;
    city?: string;
    state?: string;
    zip?: string;
    references?: string;
  };
  products?: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
    weight?: number;
    dimensions?: string;
    special_handling?: {
      fragile?: boolean;
      perishable?: boolean;
      valuable?: boolean;
      hazardous?: boolean;
      refrigerated?: boolean;
      oversized?: boolean;
    };
  }>;
  delivery_mode?: string;
  delivery_date?: string;
  delivery_time_slot?: string;
  pickup_time_slot?: string;
  payment_method?: string;
  invoice_required?: boolean;
  fiscal_data?: {
    rfc?: string;
    business_name?: string;
    tax_regime?: string;
  };
  special_instructions?: string;
  allow_substitutions?: boolean;
  communication_preferences?: string[];
  vehicle_type_id?: string;
  cargo_type_id?: string;
  route_optimization?: string;
  delivery_region?: string;
  total_cost?: number;
  estimated_delivery_time?: string;
  terms_accepted?: boolean;
  stops?: Array<{
    id: string;
    address: string;
    city: string;
    state: string;
    zip: string;
    scheduled_time?: string;
    instructions?: string;
  }>;
}

interface UpdateOrderRequest {
  status?: string;
  pickup_address?: object | string;
  delivery_addresses?: Array<object | string>;
  package_details?: {
    weight?: string | number;
    dimensions?: string | number;
    description?: string;
  };
  total_cost?: number;
  payment_status?: string;
  payment_method?: string;
  delivery_id?: string;
  vehicle_type_id?: string;
  cargo_type_id?: string;
  delivery_mode?: string;
  scheduled_pickup_time?: string;
  scheduled_delivery_time?: string;
  delivery_instructions?: string;
  special_handling_notes?: string;
}

interface CostCalculationRequest {
  pickup_address?: object;
  delivery_address?: object;
  products?: Array<{
    weight?: number;
    dimensions?: object;
    special_handling?: object;
  }>;
  vehicle_type_id?: string;
  cargo_type_id?: string;
  delivery_mode?: string;
  delivery_region?: string;
  route_optimization?: string;
}

/**
 * API Client utility for making authenticated requests to the backend
 */
export class ApiClient {
  private supabase = createClient();

  /**
   * Get authentication headers for API requests
   */
  private async getAuthHeaders(): Promise<Record<string, string>> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      // Add CSRF protection header (required by security middleware)
      'X-Requested-With': 'XMLHttpRequest',
    };

    try {
      const {
        data: { session },
      } = await this.supabase.auth.getSession();

      if (session?.access_token) {
        headers['Authorization'] = `Bearer ${session.access_token}`;
      }
    } catch (error) {
      console.error('Error getting auth headers:', error);
    }

    return headers;
  }

  /**
   * Make an authenticated API request
   */
  private async makeRequest<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<{ data?: T; error?: string; success: boolean }> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(url, {
        ...options,
        headers: {
          ...headers,
          ...options.headers,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error:
            result.error || `HTTP ${response.status}: ${response.statusText}`,
        };
      }

      return {
        success: true,
        data: result.data || result,
      };
    } catch (error) {
      console.error('API request error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Create a regular order
   */
  async createOrder(orderData: CreateOrderRequest) {
    return this.makeRequest('/api/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  }

  /**
   * Create a multi-stop order
   */
  async createMultiStopOrder(orderData: CreateOrderRequest) {
    return this.makeRequest('/api/orders/multi-stop', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  }

  /**
   * Update an order
   */
  async updateOrder(orderId: string, orderData: UpdateOrderRequest) {
    return this.makeRequest(`/api/orders/${orderId}`, {
      method: 'PUT',
      body: JSON.stringify(orderData),
    });
  }

  /**
   * Update multi-stop order
   */
  async updateMultiStopOrder(orderId: string, orderData: UpdateOrderRequest) {
    return this.makeRequest(`/api/orders/multi-stop/${orderId}`, {
      method: 'PUT',
      body: JSON.stringify(orderData),
    });
  }

  /**
   * Get vehicle types
   */
  async getVehicleTypes() {
    return this.makeRequest('/api/vehicle-types');
  }

  /**
   * Get cargo types
   */
  async getCargoTypes() {
    return this.makeRequest('/api/cargo-types');
  }

  /**
   * Calculate cost
   */
  async calculateCost(costData: CostCalculationRequest) {
    return this.makeRequest('/api/cost-calculation', {
      method: 'POST',
      body: JSON.stringify(costData),
    });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

/**
 * Transform order data from frontend format to API format for multi-stop orders
 */
export function transformToMultiStopApiFormat(
  orderData: OrderFormData
): CreateOrderRequest {
  // Convert stops to delivery_addresses format
  const delivery_addresses =
    orderData.stops?.map(stop => ({
      address: stop.address || '',
      city: stop.city || '',
      state: stop.state || '',
      zip_code: stop.zip || '',
      country: 'México',
    })) || [];

  // If no stops but has delivery_address, use that
  if (delivery_addresses.length === 0 && orderData.delivery_address) {
    delivery_addresses.push({
      address: orderData.delivery_address.street || '',
      city: orderData.delivery_address.city || '',
      state: orderData.delivery_address.state || '',
      zip_code: orderData.delivery_address.zip || '',
      country: 'México',
    });
  }

  // Transform pickup address
  const pickup_address = {
    address: orderData.pickup_address?.street || '',
    city: orderData.pickup_address?.city || '',
    state: orderData.pickup_address?.state || '',
    zip_code: orderData.pickup_address?.zip || '',
    country: 'México',
  };

  // Transform products to package_details
  const package_details =
    orderData.products?.length > 0
      ? {
          weight: orderData.products.reduce(
            (total: number, product) => total + (product.weight || 1),
            0
          ),
          dimensions: `${orderData.products.length} items`,
          description: orderData.products
            .map(p => `${p.quantity}x ${p.name}`)
            .join(', '),
          fragile:
            orderData.products.some(p => p.special_handling?.fragile) || false,
        }
      : null;

  return {
    customer_id: orderData.customer_id,
    pickup_address,
    delivery_addresses,
    package_details,
    vehicle_type_id: orderData.vehicle_type_id,
    cargo_type_id: orderData.cargo_type_id,
    delivery_mode: orderData.delivery_mode,
    scheduled_pickup_time:
      orderData.delivery_date && orderData.pickup_time_slot
        ? `${orderData.delivery_date}T${orderData.pickup_time_slot}:00`
        : null,
    scheduled_delivery_time:
      orderData.delivery_date && orderData.delivery_time_slot
        ? `${orderData.delivery_date}T${orderData.delivery_time_slot}:00`
        : null,
    delivery_instructions: orderData.special_instructions,
    special_handling_notes: orderData.special_instructions,
    fiscal_data: orderData.fiscal_data,
    payment_method: orderData.payment_method,
    payment_method_details: null,
    stops: orderData.stops,
    route_optimization: orderData.route_optimization,
  };
}

/**
 * Check if an order is multi-stop
 */
export function isMultiStopOrder(
  orderData: OrderFormData | CreateOrderRequest
): boolean {
  return (
    orderData.delivery_mode === 'multi' ||
    (orderData.stops &&
      Array.isArray(orderData.stops) &&
      orderData.stops.length > 0)
  );
}
