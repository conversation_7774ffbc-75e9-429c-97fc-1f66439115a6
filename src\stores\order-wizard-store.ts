import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { OrderFormData } from '@/types/order-form';

interface OrderWizardState {
  // Form data
  formData: OrderFormData;

  // Wizard state
  currentStep: number;
  errors: Record<string, string>;
  isSubmitting: boolean;

  // Actions
  updateFormData: (data: Partial<OrderFormData>) => void;
  setCurrentStep: (step: number) => void;
  setErrors: (errors: Record<string, string>) => void;
  setIsSubmitting: (isSubmitting: boolean) => void;
  resetWizard: () => void;
  clearErrors: () => void;
}

// Default form data
const defaultFormData: OrderFormData = {
  // Step 1: Customer Information
  customer_name: '',
  customer_phone: '',
  customer_email: '',
  customer_type: 'individual',

  // Step 2: Addresses
  pickup_address: {
    id: 'pickup-default',
    street: 'Av. Insurgentes Sur',
    number: '1234',
    colony: 'Del Valle',
    city: 'Ciudad de México',
    state: 'Ciudad de México',
    zip: '03100',
    references: 'Oficina principal - Mouvers',
  },
  delivery_address: {
    id: '1',
    street: '',
    number: '',
    colony: '',
    city: '',
    state: 'Ciudad de México',
    zip: '',
    references: '',
  },

  // Step 3: Products
  products: [
    {
      id: '1',
      name: '',
      quantity: 1,
      unit_measure: 'kg',
      unit_price: 0,
      subtotal: 0,
      weight: 1,
      weight_unit: 'kg',
      dimensions: {
        length: 10,
        width: 10,
        height: 10,
        unit: 'cm',
      },
      special_handling: {
        fragile: false,
        perishable: false,
        valuable: false,
        hazardous: false,
        refrigerated: false,
        oversized: false,
      },
      notes: '',
    },
  ],

  // Step 4: Delivery Options
  delivery_date: '',
  delivery_time_slot: '',
  delivery_mode: 'domicilio',

  // Step 5: Multi-stop (handled by delivery_mode)

  // Step 6: Payment
  payment_method: 'cash',
  invoice_required: false,
  fiscal_data: {
    rfc: '',
    business_name: '',
    tax_regime: '',
  },
  payment_method_details: {
    card_last_four: '',
    digital_wallet_provider: '',
    bank_account_last_four: '',
  },

  // Step 7: Instructions and Policies
  special_instructions: '',
  allow_substitutions: false,
  terms_accepted: false,

  // Step 8: Vehicle and Logistics
  vehicle_type_id: '********-89ab-cdef-0123-456789abcdef', // Default vehicle type for testing
  cargo_type_id: '********-5bd3-4594-9ef6-9f100f9baa9c', // Default to "Carga Perecedera" for perishable goods
  scheduled_pickup_time: '',
  scheduled_delivery_time: '',
  delivery_instructions: '',
  special_handling_notes: '',
  route_optimization: 'balanced',
  delivery_zone: '',
  delivery_region: 'local',

  // Additional fields
  stops: [],
  tracking_number: '',
};

export const useOrderWizardStore = create<OrderWizardState>()(
  persist(
    (set, get) => ({
      // Initial state
      formData: defaultFormData,
      currentStep: 1,
      errors: {},
      isSubmitting: false,

      // Actions
      updateFormData: (data: Partial<OrderFormData>) => {
        set(state => ({
          formData: { ...state.formData, ...data },
          // Clear errors when data is updated
          errors: {},
        }));
      },

      setCurrentStep: (step: number) => {
        set({ currentStep: step });
      },

      setErrors: (errors: Record<string, string>) => {
        set({ errors });
      },

      setIsSubmitting: (isSubmitting: boolean) => {
        set({ isSubmitting });
      },

      clearErrors: () => {
        set({ errors: {} });
      },

      resetWizard: () => {
        set({
          formData: defaultFormData,
          currentStep: 1,
          errors: {},
          isSubmitting: false,
        });
      },
    }),
    {
      name: 'order-wizard-storage',
      storage: createJSONStorage(() => localStorage),
      // Only persist form data and current step, not errors or submission state
      partialize: state => ({
        formData: state.formData,
        currentStep: state.currentStep,
      }),
    }
  )
);
