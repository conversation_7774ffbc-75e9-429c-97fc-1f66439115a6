'use client';

import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { OrderFormData } from '@/types/order-form';

interface Step6Props {
  formData: OrderFormData;
  updateFormData: (data: Partial<OrderFormData>) => void;
}

export function OrderWizardStep6({ formData, updateFormData }: Step6Props) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          💳 Método de Pago y Facturación
        </CardTitle>
        <CardDescription>
          Selecciona cómo deseas pagar tu pedido y configura la facturación
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-6'>
        {/* Payment Method Selection */}
        <div className='space-y-4'>
          <Label className='text-base font-medium'>Método de Pago</Label>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <label className='flex items-center space-x-2 p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors'>
              <input
                type='radio'
                name='payment_method'
                value='cash'
                checked={formData.payment_method === 'cash'}
                onChange={e =>
                  updateFormData({
                    payment_method: e.target.value as
                      | 'wallet'
                      | 'cash'
                      | 'mixed',
                  })
                }
                className='text-blue-600'
              />
              <div className='flex-1'>
                <div className='flex items-center space-x-2'>
                  <span className='text-2xl'>💵</span>
                  <span className='font-medium'>Efectivo contra entrega</span>
                </div>
                <p className='text-sm text-gray-600 mt-1'>
                  Pago en efectivo al recibir el pedido
                </p>
              </div>
            </label>

            <label className='flex items-center space-x-2 p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors'>
              <input
                type='radio'
                name='payment_method'
                value='wallet'
                checked={formData.payment_method === 'wallet'}
                onChange={e =>
                  updateFormData({
                    payment_method: e.target.value as
                      | 'wallet'
                      | 'stripe'
                      | 'mixed',
                  })
                }
                className='text-blue-600'
              />
              <div className='flex-1'>
                <div className='flex items-center space-x-2'>
                  <span className='text-2xl'>📱</span>
                  <span className='font-medium'>Billetera Digital</span>
                </div>
                <p className='text-sm text-gray-600 mt-1'>
                  PayPal, Mercado Pago, OXXO Pay
                </p>
              </div>
            </label>

            <label className='flex items-center space-x-2 p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors'>
              <input
                type='radio'
                name='payment_method'
                value='mixed'
                checked={formData.payment_method === 'mixed'}
                onChange={e =>
                  updateFormData({
                    payment_method: e.target.value as
                      | 'wallet'
                      | 'stripe'
                      | 'mixed',
                  })
                }
                className='text-blue-600'
              />
              <div className='flex-1'>
                <div className='flex items-center space-x-2'>
                  <span className='text-2xl'>🔄</span>
                  <span className='font-medium'>Pago Mixto</span>
                </div>
                <p className='text-sm text-gray-600 mt-1'>
                  Combinación de métodos de pago
                </p>
              </div>
            </label>
          </div>
        </div>

        {/* Invoice Requirements */}
        <div className='space-y-4'>
          <Label className='text-base font-medium'>Facturación</Label>
          <div className='space-y-4'>
            <label className='flex items-center space-x-3'>
              <input
                type='checkbox'
                checked={formData.invoice_required}
                onChange={e =>
                  updateFormData({
                    invoice_required: e.target.checked,
                  })
                }
                className='text-blue-600 h-4 w-4'
              />
              <span className='font-medium'>Requiero factura (CFDI)</span>
            </label>

            {formData.invoice_required && (
              <div className='bg-blue-50 border border-blue-200 rounded-lg p-4 space-y-4'>
                <h4 className='font-medium text-blue-800'>Datos Fiscales</h4>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='fiscal_rfc'>RFC *</Label>
                    <Input
                      id='fiscal_rfc'
                      value={formData.fiscal_data?.rfc || ''}
                      onChange={e =>
                        updateFormData({
                          fiscal_data: {
                            ...formData.fiscal_data,
                            rfc: e.target.value.toUpperCase(),
                          },
                        })
                      }
                      placeholder='XAXX010101000'
                      maxLength={13}
                      required={formData.invoice_required}
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor='fiscal_business_name'>Razón Social *</Label>
                    <Input
                      id='fiscal_business_name'
                      value={formData.fiscal_data?.business_name || ''}
                      onChange={e =>
                        updateFormData({
                          fiscal_data: {
                            ...formData.fiscal_data,
                            business_name: e.target.value,
                          },
                        })
                      }
                      placeholder='Nombre o razón social'
                      required={formData.invoice_required}
                    />
                  </div>
                  <div className='space-y-2 md:col-span-2'>
                    <Label htmlFor='fiscal_tax_regime'>Régimen Fiscal</Label>
                    <select
                      id='fiscal_tax_regime'
                      value={formData.fiscal_data?.tax_regime || ''}
                      onChange={e =>
                        updateFormData({
                          fiscal_data: {
                            ...formData.fiscal_data,
                            tax_regime: e.target.value,
                          },
                        })
                      }
                      className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                    >
                      <option value=''>Seleccionar régimen fiscal</option>
                      <option value='Régimen General de Ley Personas Morales'>
                        Régimen General de Ley Personas Morales
                      </option>
                      <option value='Personas Físicas con Actividades Empresariales'>
                        Personas Físicas con Actividades Empresariales
                      </option>
                      <option value='Régimen de Incorporación Fiscal'>
                        Régimen de Incorporación Fiscal
                      </option>
                      <option value='Régimen Simplificado de Confianza'>
                        Régimen Simplificado de Confianza
                      </option>
                      <option value='Personas Físicas con Actividades Profesionales'>
                        Personas Físicas con Actividades Profesionales
                      </option>
                      <option value='Arrendamiento'>Arrendamiento</option>
                      <option value='Sin Obligaciones Fiscales'>
                        Sin Obligaciones Fiscales
                      </option>
                    </select>
                  </div>
                </div>
                <div className='text-sm text-blue-700 bg-blue-100 p-3 rounded'>
                  <strong>Nota:</strong> La factura será enviada por correo
                  electrónico en formato PDF y XML dentro de las 24 horas
                  posteriores a la entrega.
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Payment Method Details */}
        {formData.payment_method === 'card' && (
          <div className='bg-gray-50 border border-gray-200 rounded-lg p-4'>
            <h4 className='font-medium text-gray-800 mb-3'>
              Información de Tarjeta
            </h4>
            <p className='text-sm text-gray-600'>
              Los datos de tu tarjeta serán procesados de forma segura en el
              siguiente paso. Aceptamos todas las tarjetas principales con
              tecnología de encriptación SSL.
            </p>
          </div>
        )}

        {formData.payment_method === 'digital_wallet' && (
          <div className='bg-gray-50 border border-gray-200 rounded-lg p-4'>
            <h4 className='font-medium text-gray-800 mb-3'>
              Billetera Digital
            </h4>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-3'>
              <label className='flex items-center space-x-2'>
                <input
                  type='radio'
                  name='digital_wallet_provider'
                  value='paypal'
                  checked={
                    formData.payment_method_details?.digital_wallet_provider ===
                    'paypal'
                  }
                  onChange={e =>
                    updateFormData({
                      payment_method_details: {
                        ...formData.payment_method_details,
                        digital_wallet_provider: e.target.value as
                          | 'paypal'
                          | 'mercado_pago'
                          | 'oxxo_pay'
                          | 'spei',
                      },
                    })
                  }
                  className='text-blue-600'
                />
                <span className='text-sm'>PayPal</span>
              </label>
              <label className='flex items-center space-x-2'>
                <input
                  type='radio'
                  name='digital_wallet_provider'
                  value='mercado_pago'
                  checked={
                    formData.payment_method_details?.digital_wallet_provider ===
                    'mercado_pago'
                  }
                  onChange={e =>
                    updateFormData({
                      payment_method_details: {
                        ...formData.payment_method_details,
                        digital_wallet_provider: e.target.value as
                          | 'paypal'
                          | 'mercado_pago'
                          | 'oxxo_pay'
                          | 'spei',
                      },
                    })
                  }
                  className='text-blue-600'
                />
                <span className='text-sm'>Mercado Pago</span>
              </label>
              <label className='flex items-center space-x-2'>
                <input
                  type='radio'
                  name='digital_wallet_provider'
                  value='oxxo_pay'
                  checked={
                    formData.payment_method_details?.digital_wallet_provider ===
                    'oxxo_pay'
                  }
                  onChange={e =>
                    updateFormData({
                      payment_method_details: {
                        ...formData.payment_method_details,
                        digital_wallet_provider: e.target.value as
                          | 'paypal'
                          | 'mercado_pago'
                          | 'oxxo_pay'
                          | 'spei',
                      },
                    })
                  }
                  className='text-blue-600'
                />
                <span className='text-sm'>OXXO Pay</span>
              </label>
            </div>
          </div>
        )}

        {formData.payment_method === 'bank_transfer' && (
          <div className='bg-gray-50 border border-gray-200 rounded-lg p-4'>
            <h4 className='font-medium text-gray-800 mb-3'>
              Transferencia Bancaria
            </h4>
            <p className='text-sm text-gray-600 mb-3'>
              Recibirás los datos bancarios para realizar la transferencia SPEI
              después de confirmar tu pedido.
            </p>
            <div className='text-sm text-amber-700 bg-amber-100 p-3 rounded'>
              <strong>Importante:</strong> El pedido será procesado una vez que
              se confirme el pago. Tiempo estimado de confirmación: 1-2 horas
              hábiles.
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
