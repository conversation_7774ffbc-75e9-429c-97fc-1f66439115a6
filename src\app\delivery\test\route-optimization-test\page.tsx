'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  useRouteOptimization,
  Location,
  TrafficInsights,
} from '@/delivery/hooks/useRouteOptimization';

interface AdvancedOptimizationResult {
  totalDistance: number;
  totalDuration: number;
}

export default function RouteOptimizationTest() {
  const {
    optimizeRoute,
    optimizeRouteAdvanced,
    getTrafficInsights,
    validateTimeWindows,
  } = useRouteOptimization();
  const [testResults, setTestResults] = useState<{
    basicOptimization?: Location[];
    advancedOptimization?: AdvancedOptimizationResult;
    trafficInsights?: TrafficInsights;
    timeWindowValid?: boolean;
    timestamp?: string;
    error?: string;
    details?: string;
  } | null>(null);
  const [loading, setLoading] = useState(false);

  // Sample Mexican logistics data
  const sampleLocations: Location[] = [
    {
      id: 'warehouse',
      name: '<PERSON><PERSON><PERSON>',
      lat: 25.6866,
      lng: -100.3161,
      deliveryWindowStart: '08:00',
      deliveryWindowEnd: '18:00',
    },
    {
      id: 'customer1',
      name: 'Cliente Guadalajara',
      lat: 20.6597,
      lng: -103.3496,
      deliveryWindowStart: '09:00',
      deliveryWindowEnd: '17:00',
    },
    {
      id: 'customer2',
      name: 'Cliente Ciudad de México',
      lat: 19.4326,
      lng: -99.1332,
      deliveryWindowStart: '10:00',
      deliveryWindowEnd: '16:00',
    },
    {
      id: 'customer3',
      name: 'Cliente Puebla',
      lat: 19.0414,
      lng: -98.2063,
      deliveryWindowStart: '11:00',
      deliveryWindowEnd: '15:00',
    },
  ];

  const runTests = async () => {
    setLoading(true);

    try {
      // Test basic route optimization
      const basicOptimization = optimizeRoute(sampleLocations);

      // Test advanced route optimization
      const advancedOptimization = optimizeRouteAdvanced(
        sampleLocations,
        { maxWeight: 1000, maxVolume: 20 },
        '08:00'
      );

      // Test traffic insights
      const trafficInsights = getTrafficInsights();

      // Test time window validation
      const timeWindowValid = validateTimeWindows(advancedOptimization);

      setTestResults({
        basicOptimization,
        advancedOptimization,
        trafficInsights,
        timeWindowValid,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Test error:', error);
      setTestResults({
        error: 'Error during testing',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='container mx-auto py-8'>
      <h1 className='text-3xl font-bold mb-6'>
        Pruebas de Optimización de Rutas
      </h1>

      <div className='mb-6'>
        <Button onClick={runTests} disabled={loading}>
          {loading
            ? 'Ejecutando Pruebas...'
            : 'Ejecutar Pruebas de Optimización'}
        </Button>
      </div>

      {testResults && (
        <div className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Resultados de Pruebas</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className='bg-gray-100 p-4 rounded-lg overflow-auto max-h-96 text-sm'>
                {JSON.stringify(testResults, null, 2)}
              </pre>
            </CardContent>
          </Card>

          {testResults.basicOptimization && (
            <Card>
              <CardHeader>
                <CardTitle>Optimización Básica</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-2'>
                  <h3 className='font-medium'>Orden de Ruta:</h3>
                  <ol className='list-decimal list-inside space-y-1'>
                    {testResults.basicOptimization.map((location, index) => (
                      <li key={location.id}>{location.name}</li>
                    ))}
                  </ol>
                </div>
              </CardContent>
            </Card>
          )}

          {testResults.advancedOptimization && (
            <Card>
              <CardHeader>
                <CardTitle>Optimización Avanzada</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                  <div className='bg-blue-50 p-4 rounded-lg'>
                    <h3 className='font-medium text-blue-800'>
                      Distancia Total
                    </h3>
                    <p className='text-2xl font-bold'>
                      {testResults.advancedOptimization.totalDistance.toFixed(
                        2
                      )}{' '}
                      km
                    </p>
                  </div>
                  <div className='bg-green-50 p-4 rounded-lg'>
                    <h3 className='font-medium text-green-800'>
                      Duración Total
                    </h3>
                    <p className='text-2xl font-bold'>
                      {testResults.advancedOptimization.totalDuration.toFixed(
                        0
                      )}{' '}
                      min
                    </p>
                  </div>
                  <div className='bg-purple-50 p-4 rounded-lg'>
                    <h3 className='font-medium text-purple-800'>
                      Validación de Horarios
                    </h3>
                    <p className='text-2xl font-bold'>
                      {testResults.timeWindowValid ? '✅' : '❌'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {!testResults && !loading && (
        <Card>
          <CardHeader>
            <CardTitle>Instrucciones</CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-gray-600'>
              Haz clic en &quot;Ejecutar Pruebas de Optimización&quot; para
              probar la implementación de optimización de rutas con datos de
              muestra de logística mexicana. Los resultados mostrarán la ruta
              optimizada, información de tráfico y validación de ventanas de
              tiempo de entrega.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
